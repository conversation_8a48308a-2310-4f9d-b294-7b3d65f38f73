-- Database updates to change teen_responses age range from 13-17 to 10-17
-- Run these queries in your Supabase SQL Editor

-- 1. Check current age distribution in teen_responses table
SELECT age, COUNT(*) as count 
FROM teen_responses 
GROUP BY age 
ORDER BY age;

-- 2. Add age check constraint to ensure only ages 10-17 can be stored
ALTER TABLE teen_responses 
ADD CONSTRAINT teen_responses_age_check 
CHECK (age >= 10 AND age <= 17);

-- 3. Update table comment to reflect new age range
COMMENT ON TABLE teen_responses IS 'Detailed responses for users aged 10-17';

-- 4. Verify the constraint was added successfully
SELECT conname, consrc 
FROM pg_constraint 
WHERE conrelid = 'teen_responses'::regclass 
AND contype = 'c';

-- 5. Test the constraint by trying to insert invalid ages (should fail)
-- Uncomment these lines to test (they should fail):
-- INSERT INTO teen_responses (response_id, name, age, preferred_language, high_risk, medium_risk, low_risk, overall_risk) 
-- VALUES (999, 'Test User', 9, 'english', 0, 0, 1, 'Low');  -- Should fail (age 9)

-- INSERT INTO teen_responses (response_id, name, age, preferred_language, high_risk, medium_risk, low_risk, overall_risk) 
-- VALUES (999, 'Test User', 18, 'english', 0, 0, 1, 'Low'); -- Should fail (age 18)

-- 6. Test valid age insertion (should succeed)
-- Uncomment this line to test (should succeed):
-- INSERT INTO teen_responses (response_id, name, age, preferred_language, high_risk, medium_risk, low_risk, overall_risk) 
-- VALUES (999, 'Test User', 10, 'english', 0, 0, 1, 'Low'); -- Should succeed (age 10)

-- Clean up test data (run after testing)
-- DELETE FROM teen_responses WHERE response_id = 999;
