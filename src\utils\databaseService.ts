import {
  supabase,
  ScamRiskResult,
  TeenR<PERSON>po<PERSON>,
  TeenResponseWithMain,
} from "../lib/supabase";
import { User, Response } from "../types";
import { calculateRisk, getRiskProfile } from "./riskCalculator";

export interface DatabaseResult extends ScamRiskResult {
  id: number;
  created_at: string;
}

export class DatabaseService {
  /**
   * Save assessment results to Supabase
   * For users aged 10-17, also saves to teen_responses table
   */
  static async saveResults(
    user: User,
    responses: Response[]
  ): Promise<DatabaseResult | null> {
    try {
      const riskCounts = calculateRisk(responses);
      const riskProfile = getRiskProfile(riskCounts);

      // Convert risk level to database format - use capitalized first letter
      let overallRiskValue: string;
      switch (riskProfile.level) {
        case "High Risk":
          overallRiskValue = "High";
          break;
        case "Medium Risk":
          overallRiskValue = "Medium";
          break;
        case "Low Risk":
          overallRiskValue = "Low";
          break;
        default:
          overallRiskValue = "Medium"; // fallback
      }

      const resultData: ScamRiskResult = {
        name: user.name,
        age: user.age,
        preferred_language: user.language,
        high_risk: riskCounts.high_risk,
        medium_risk: riskCounts.medium_risk,
        low_risk: riskCounts.low_risk,
        overall_risk: overallRiskValue,
      };

      // First, save to scam_risk_results table
      const { data, error } = await supabase
        .from("scam_risk_results")
        .insert([resultData])
        .select()
        .single();

      if (error) {
        console.error("Error saving results:", error);
        return null;
      }

      const mainResult = data as DatabaseResult;

      // If user is 10-17, also save to teen_responses table
      if (user.age >= 10 && user.age <= 17) {
        await this.saveTeenResponses(mainResult.id, user, responses);
      }

      return mainResult;
    } catch (error) {
      console.error("Error in saveResults:", error);
      return null;
    }
  }

  /**
   * Save teen-specific responses to teen_responses table
   */
  private static async saveTeenResponses(
    scamRiskResultsId: number,
    user: User,
    responses: Response[]
  ): Promise<void> {
    try {
      const riskCounts = calculateRisk(responses);
      const riskProfile = getRiskProfile(riskCounts);

      // Convert risk level to database format
      let overallRiskValue: string;
      switch (riskProfile.level) {
        case "High Risk":
          overallRiskValue = "High";
          break;
        case "Medium Risk":
          overallRiskValue = "Medium";
          break;
        case "Low Risk":
          overallRiskValue = "Low";
          break;
        default:
          overallRiskValue = "Medium";
      }

      // Initialize scenario risk tracking object with all required fields
      const scenarioRiskIssues: Omit<TeenResponse, "id" | "created_at"> = {
        response_id: scamRiskResultsId,
        name: user.name,
        age: user.age,
        preferred_language: user.language,
        high_risk: riskCounts.high_risk,
        medium_risk: riskCounts.medium_risk,
        low_risk: riskCounts.low_risk,
        overall_risk: overallRiskValue,
        scenario_1_risk_issues: [],
        scenario_2_risk_issues: [],
        scenario_3_risk_issues: [],
        scenario_4_risk_issues: [],
        scenario_5_risk_issues: [],
        scenario_6_risk_issues: [],
      };

      // Process each response and map to appropriate scenario column
      responses.forEach((response, index) => {
        const scenarioNumber = index + 1; // Scenarios are 1-indexed

        // Track if this response was medium or high risk
        if (
          response.riskLevel === "medium_risk" ||
          response.riskLevel === "high_risk"
        ) {
          // Store the risk level and could add more specific indicators later
          const riskIndicator = response.riskLevel;

          switch (scenarioNumber) {
            case 1:
              scenarioRiskIssues.scenario_1_risk_issues = [riskIndicator];
              break;
            case 2:
              scenarioRiskIssues.scenario_2_risk_issues = [riskIndicator];
              break;
            case 3:
              scenarioRiskIssues.scenario_3_risk_issues = [riskIndicator];
              break;
            case 4:
              scenarioRiskIssues.scenario_4_risk_issues = [riskIndicator];
              break;
            case 5:
              scenarioRiskIssues.scenario_5_risk_issues = [riskIndicator];
              break;
            case 6:
              scenarioRiskIssues.scenario_6_risk_issues = [riskIndicator];
              break;
          }
        }
      });

      console.log("Attempting to save teen response:", scenarioRiskIssues);

      // Insert into teen_responses table
      const { data: teenData, error: teenError } = await supabase
        .from("teen_responses")
        .insert([scenarioRiskIssues])
        .select();

      if (teenError) {
        console.error("Error saving teen responses:", teenError);
        console.error("Error details:", {
          message: teenError.message,
          details: teenError.details,
          hint: teenError.hint,
          code: teenError.code,
        });
      } else {
        console.log("Teen responses saved successfully:", teenData);
      }
    } catch (error) {
      console.error("Error in saveTeenResponses:", error);
    }
  }

  /**
   * Get all results from the database
   */
  static async getAllResults(): Promise<DatabaseResult[]> {
    try {
      const { data, error } = await supabase
        .from("scam_risk_results")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching results:", error);
        return [];
      }

      return data as DatabaseResult[];
    } catch (error) {
      console.error("Error in getAllResults:", error);
      return [];
    }
  }

  /**
   * Get results by language
   */
  static async getResultsByLanguage(
    language: string
  ): Promise<DatabaseResult[]> {
    try {
      const { data, error } = await supabase
        .from("scam_risk_results")
        .select("*")
        .eq("preferred_language", language)
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching results by language:", error);
        return [];
      }

      return data as DatabaseResult[];
    } catch (error) {
      console.error("Error in getResultsByLanguage:", error);
      return [];
    }
  }

  /**
   * Get results statistics
   */
  static async getStatistics(): Promise<{
    total: number;
    byRiskLevel: Record<string, number>;
    byLanguage: Record<string, number>;
    byAgeGroup: Record<string, number>;
  }> {
    try {
      const { data, error } = await supabase
        .from("scam_risk_results")
        .select("*");

      if (error) {
        console.error("Error fetching statistics:", error);
        return {
          total: 0,
          byRiskLevel: {},
          byLanguage: {},
          byAgeGroup: {},
        };
      }

      const results = data as DatabaseResult[];
      const total = results.length;

      // Group by risk level
      const byRiskLevel = results.reduce((acc, result) => {
        acc[result.overall_risk] = (acc[result.overall_risk] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Group by language
      const byLanguage = results.reduce((acc, result) => {
        acc[result.preferred_language] =
          (acc[result.preferred_language] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Group by age group
      const byAgeGroup = results.reduce((acc, result) => {
        let ageGroup: string;
        if (result.age <= 17) ageGroup = "10-17";
        else if (result.age <= 30) ageGroup = "18-30";
        else if (result.age <= 45) ageGroup = "31-45";
        else if (result.age <= 60) ageGroup = "46-60";
        else ageGroup = "61+";

        acc[ageGroup] = (acc[ageGroup] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      return {
        total,
        byRiskLevel,
        byLanguage,
        byAgeGroup,
      };
    } catch (error) {
      console.error("Error in getStatistics:", error);
      return {
        total: 0,
        byRiskLevel: {},
        byLanguage: {},
        byAgeGroup: {},
      };
    }
  }

  /**
   * Test database connection
   */
  static async testConnection(): Promise<boolean> {
    try {
      const { error } = await supabase
        .from("scam_risk_results")
        .select("count", { count: "exact", head: true });

      if (error) {
        console.error("Database connection test failed:", error);
        return false;
      }

      console.log("Database connection successful");
      return true;
    } catch (error) {
      console.error("Database connection test error:", error);
      return false;
    }
  }

  /**
   * Get teen responses with main assessment data
   */
  static async getTeenResponses(): Promise<TeenResponseWithMain[]> {
    try {
      const { data, error } = await supabase
        .from("teen_responses_with_main")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching teen responses:", error);
        return [];
      }

      return (data as TeenResponseWithMain[]) || [];
    } catch (error) {
      console.error("Error in getTeenResponses:", error);
      return [];
    }
  }

  /**
   * Get teen responses by specific risk scenario
   */
  static async getTeenResponsesByScenario(
    scenarioNumber: number
  ): Promise<TeenResponseWithMain[]> {
    try {
      const columnName = `scenario_${scenarioNumber}_risk_issues`;

      const { data, error } = await supabase
        .from("teen_responses_with_main")
        .select("*")
        .not(columnName, "eq", "[]") // Get responses where the scenario array is not empty
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching teen responses by scenario:", error);
        return [];
      }

      return (data as TeenResponseWithMain[]) || [];
    } catch (error) {
      console.error("Error in getTeenResponsesByScenario:", error);
      return [];
    }
  }
}
