-- IMMEDIATE FIX for teen_responses age constraint issue
-- Run these queries ONE BY ONE in your Supabase SQL Editor

-- Step 1: Check what constraints currently exist
SELECT conname, pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint
WHERE conrelid = 'teen_responses'::regclass
AND contype = 'c';

-- Step 2: Drop the problematic constraint (run this after seeing the constraint name from Step 1)
-- Replace 'teen_responses_age_check' with the actual constraint name if different
ALTER TABLE teen_responses DROP CONSTRAINT IF EXISTS teen_responses_age_check;

-- Step 3: Drop any other age-related constraints that might exist
ALTER TABLE teen_responses DROP CONSTRAINT IF EXISTS teen_responses_age_check_10_17;
ALTER TABLE teen_responses DROP CONSTRAINT IF EXISTS check_age_range;
ALTER TABLE teen_responses DROP CONSTRAINT IF EXISTS age_constraint;

-- Step 4: Add the correct constraint for ages 10-17
ALTER TABLE teen_responses 
ADD CONSTRAINT teen_responses_age_10_17 
CHECK (age >= 10 AND age <= 17);

-- Step 5: Verify the new constraint is in place
SELECT conname, pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint
WHERE conrelid = 'teen_responses'::regclass
AND contype = 'c';

-- Step 6: Test with a valid age (should succeed)
-- Uncomment to test:
-- INSERT INTO teen_responses (response_id, name, age, preferred_language, high_risk, medium_risk, low_risk, overall_risk) 
-- VALUES (9999, 'Test User', 10, 'english', 0, 0, 1, 'Low');

-- Step 7: Clean up test data
-- DELETE FROM teen_responses WHERE response_id = 9999;
