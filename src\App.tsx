import React, { useState, useEffect, useRef } from "react";
import InitialForm from "./components/InitialForm";
import Survey from "./components/Survey";
import Results from "./components/Results";
import { User, Response, SurveyState } from "./types";
import { getScenarios } from "./utils/language";

function App() {
  const [surveyState, setSurveyState] = useState<SurveyState>({
    user: null,
    currentScenario: 0,
    responses: [],
    isComplete: false,
  });

  const hasPlayedAudioRef = useRef(false);
  const [showAudioPrompt, setShowAudioPrompt] = useState(false);
  const [showMascot, setShowMascot] = useState(false);
  const [currentText, setCurrentText] = useState("");
  const [showGlitchPopups, setShowGlitchPopups] = useState(false);
  const [currentPopupIndex, setCurrentPopupIndex] = useState(0);
  const [showScenarioIntro, setShowScenarioIntro] = useState(false);
  const [scenarioIntroText, setScenarioIntroText] = useState("");
  const [showScenario2Intro, setShowScenario2Intro] = useState(false);
  const [scenario2IntroText, setScenario2IntroText] = useState("");
  const [showScenario3Intro, setShowScenario3Intro] = useState(false);
  const [scenario3IntroText, setScenario3IntroText] = useState("");
  const [showScenario4Intro, setShowScenario4Intro] = useState(false);
  const [scenario4IntroText, setScenario4IntroText] = useState("");
  const [showScenario5Intro, setShowScenario5Intro] = useState(false);
  const [scenario5IntroText, setScenario5IntroText] = useState("");
  const [showScenario6Intro, setShowScenario6Intro] = useState(false);
  const [scenario6IntroText, setScenario6IntroText] = useState("");

  // Hacker Background Component
  const HackerBackground = ({ children }: { children: React.ReactNode }) => (
    <div className="min-h-screen bg-black relative overflow-hidden">
      {/* Moving Grid Background */}
      <div className="absolute inset-0 opacity-20">
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `
              linear-gradient(rgba(34, 197, 94, 0.3) 1px, transparent 1px),
              linear-gradient(90deg, rgba(34, 197, 94, 0.3) 1px, transparent 1px)
            `,
            backgroundSize: "25px 25px",
            animation: "gridMove 20s linear infinite",
          }}
        />
      </div>

      {/* Floating Code Lines */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Code snippets floating across screen */}
        <div className="absolute top-10 left-0 w-full">
          <div className="animate-pulse opacity-30">
            <pre
              className="text-green-400 font-mono text-xs leading-relaxed whitespace-nowrap"
              style={{ animation: "scrollLeft 25s linear infinite" }}
            >
              {`if (user.isVulnerable()) {
  exploit.execute();
  data.steal();
} else {
  security.bypass();
}`}
            </pre>
          </div>
        </div>

        <div className="absolute top-32 right-0 w-full">
          <div className="animate-pulse opacity-25 delay-1000">
            <pre
              className="text-cyan-400 font-mono text-xs leading-relaxed whitespace-nowrap text-right"
              style={{ animation: "scrollRight 30s linear infinite" }}
            >
              {`function hackMainframe() {
  const access = gainRootAccess();
  if (access.granted) {
    system.infiltrate();
    firewall.disable();
  }
}`}
            </pre>
          </div>
        </div>

        <div className="absolute top-56 left-0 w-full">
          <div className="animate-pulse opacity-20 delay-2000">
            <pre
              className="text-pink-400 font-mono text-xs leading-relaxed whitespace-nowrap"
              style={{ animation: "scrollLeft 35s linear infinite" }}
            >
              {`SELECT * FROM users WHERE password='admin123';
DROP TABLE security_logs;
INSERT INTO backdoor VALUES ('active');`}
            </pre>
          </div>
        </div>

        <div className="absolute bottom-40 right-0 w-full">
          <div className="animate-pulse opacity-15 delay-3000">
            <pre
              className="text-purple-400 font-mono text-xs leading-relaxed whitespace-nowrap text-right"
              style={{ animation: "scrollRight 20s linear infinite" }}
            >
              {`#!/bin/bash
nmap -sS -O target.com
hydra -l admin -P passwords.txt ssh://target
metasploit > use exploit/multi/handler`}
            </pre>
          </div>
        </div>

        <div className="absolute bottom-20 left-0 w-full">
          <div className="animate-pulse opacity-25 delay-4000">
            <pre
              className="text-yellow-400 font-mono text-xs leading-relaxed whitespace-nowrap"
              style={{ animation: "scrollLeft 28s linear infinite" }}
            >
              {`const decrypt = (data) => {
  return CryptoJS.AES.decrypt(data, secretKey).toString();
};
payload.inject(targetProcess);`}
            </pre>
          </div>
        </div>
      </div>

      {/* Terminal windows */}
      <div className="absolute top-20 left-20 w-80 h-48 bg-black/80 border border-green-500/30 rounded-lg overflow-hidden opacity-20">
        <div className="bg-green-500/20 px-4 py-2 text-green-400 font-mono text-xs">
          root@hackersim:~#
        </div>
        <div className="p-4 text-green-400 font-mono text-xs leading-relaxed">
          <div className="animate-pulse">
            $ sudo nmap -sV -sC target.network
            <br />
            Starting Nmap scan...
            <br />
            Host is up (0.001s latency)
            <br />
            PORT STATE SERVICE
            <br />
            22/tcp open ssh
            <br />
            80/tcp open http
            <br />
            443/tcp open https
            <br />
            <span className="animate-ping">▋</span>
          </div>
        </div>
      </div>

      <div className="absolute bottom-20 right-20 w-72 h-40 bg-black/80 border border-cyan-500/30 rounded-lg overflow-hidden opacity-20">
        <div className="bg-cyan-500/20 px-4 py-2 text-cyan-400 font-mono text-xs">
          [BREACH DETECTED]
        </div>
        <div className="p-4 text-cyan-400 font-mono text-xs leading-relaxed">
          <div className="animate-pulse">
            System compromised...
            <br />
            Payload deployed: SUCCESS
            <br />
            Data exfiltration: 47%
            <br />
            Covering tracks...
            <br />
            <span className="animate-ping text-red-400">CRITICAL</span>
          </div>
        </div>
      </div>

      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-green-500/5 rounded-full blur-3xl animate-pulse" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500/5 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute top-20 left-20 w-60 h-60 bg-green-500/3 rounded-full blur-2xl animate-pulse delay-2000" />

        {/* Floating particles */}
        <div className="absolute top-1/4 right-1/4 w-1 h-1 bg-green-400/30 rounded-full animate-ping" />
        <div className="absolute bottom-1/3 left-1/3 w-1 h-1 bg-green-400/20 rounded-full animate-pulse" />
        <div className="absolute top-1/2 left-1/4 w-2 h-2 bg-green-400/15 rounded-full animate-ping delay-1000" />

        {/* Hacker symbols */}
        <div className="absolute top-40 right-1/3 text-green-400/10 text-6xl font-mono animate-pulse">
          {"</>"}
        </div>
        <div className="absolute bottom-60 left-1/4 text-cyan-400/10 text-4xl font-mono animate-pulse delay-1000">
          $
        </div>
        <div className="absolute top-1/3 left-1/6 text-red-400/10 text-5xl font-mono animate-pulse delay-2000">
          #
        </div>
      </div>

      {/* Global CSS Animation Styles */}
      <style
        dangerouslySetInnerHTML={{
          __html: `
          @keyframes gridMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(25px, 25px); }
          }
          @keyframes scrollLeft {
            0% { transform: translateX(100vw); }
            100% { transform: translateX(-100%); }
          }
          @keyframes scrollRight {
            0% { transform: translateX(-100vw); }
            100% { transform: translateX(100%); }
          }
        `,
        }}
      />

      {/* Content */}
      <div className="relative z-10">{children}</div>
    </div>
  );

  // Function to play button click sound
  const playClickSound = () => {
    try {
      const clickAudio = new Audio("/audio/click.mp3");
      clickAudio.volume = 0.5;
      clickAudio.play().catch((err) => console.log("Click sound failed:", err));
    } catch (error) {
      console.log("Click sound error:", error);
    }
  };

  // Play welcome audio once when the app loads
  useEffect(() => {
    const playWelcomeAudio = async () => {
      if (hasPlayedAudioRef.current) return;

      try {
        const audio = new Audio("/audio/extracted_audio.mp3");
        audio.volume = 0.7;

        // Try to play immediately
        await audio.play();
        hasPlayedAudioRef.current = true;
        console.log("Welcome audio played successfully");
      } catch {
        console.log("Audio autoplay blocked, showing user prompt");
        // Show audio prompt instead of trying multiple failed attempts
        setShowAudioPrompt(true);
      }
    };

    // Try to play immediately when component mounts
    playWelcomeAudio();
  }, []);

  // Handle audio prompt interaction
  const handleEnableAudio = async () => {
    playClickSound();
    try {
      const audio = new Audio("/audio/extracted_audio.mp3");
      audio.volume = 0.7;
      await audio.play();
      hasPlayedAudioRef.current = true;
      setShowAudioPrompt(false);

      // Show mascot and start typewriter effect
      setShowMascot(true);
      startTypewriterEffect();

      console.log("Welcome audio played after user interaction");
    } catch (error) {
      console.log("Audio play failed even after user interaction:", error);
      setShowAudioPrompt(false);

      // Show mascot even if audio fails
      setShowMascot(true);
      startTypewriterEffect();
    }
  };

  // Typewriter effect for mascot messages
  const fullMessage = `Welcome, Agent-in-Training.

You've just entered the Scam Simulation Mainframe — where the lines between real and fake blur.

Your mission: Outsmart the internet's trickiest scammers.

Be sharp. Be fast. Trust no one.

Good luck.`;

  // Scenario intro message
  const scenarioIntroMessage = `Gear up for the Instagram Reel Challenge, Agent.

Let's see how well you can navigate what's coming your way.`;

  // Scenario 2 intro message
  const scenario2IntroMessage = `You gave that first challenge a solid shot, Agent. Not bad — but things are only getting tougher from here. Ready for what's next?`;

  // Fake popup messages for glitch effect
  const fakePopups = [
    {
      title: "SYSTEM ERROR",
      message: "Connection lost to server",
      type: "error",
    },
    {
      title: "SECURITY ALERT",
      message: "Unauthorized access detected",
      type: "warning",
    },
    {
      title: "VIRUS DETECTED",
      message: "Malware found! Scanning...",
      type: "error",
    },
    {
      title: "UPDATE REQUIRED",
      message: "Please restart your browser",
      type: "info",
    },
    {
      title: "PAYMENT FAILED",
      message: "Your card has been declined",
      type: "warning",
    },
    {
      title: "ACCOUNT SUSPENDED",
      message: "Suspicious activity detected",
      type: "error",
    },
    {
      title: "WINNER!",
      message: "You've won $1000! Click here!",
      type: "success",
    },
    {
      title: "SYSTEM OVERLOAD",
      message: "Too many users online",
      type: "error",
    },
    {
      title: "FIREWALL BREACH",
      message: "System compromised",
      type: "warning",
    },
    {
      title: "LOADING...",
      message: "Please wait while we redirect you",
      type: "info",
    },
  ];

  const startTypewriterEffect = () => {
    let charIndex = 0;

    const typeNextChar = () => {
      if (charIndex < fullMessage.length) {
        setCurrentText(fullMessage.substring(0, charIndex + 1));
        charIndex++;
        setTimeout(typeNextChar, 56); // Slowed by 8% more (52 * 1.08)
      } else {
        // Start glitch popup sequence after text is complete
        setTimeout(() => {
          setShowGlitchPopups(true);
          startPopupSequence();
        }, 2000); // Give time to read the complete message
      }
    };

    typeNextChar();
  };

  const startScenarioIntroTypewriter = () => {
    let charIndex = 0;

    // Play scenario 1 audio
    const playScenario1Audio = async () => {
      try {
        const audio = new Audio("/audio/Scenario-1.mp3");
        audio.volume = 0.7;
        await audio.play();
        console.log("Scenario 1 audio played successfully");
      } catch (error) {
        console.log("Scenario 1 audio play failed:", error);
      }
    };

    // Start audio playback
    playScenario1Audio();

    const typeNextChar = () => {
      if (charIndex < scenarioIntroMessage.length) {
        setScenarioIntroText(scenarioIntroMessage.substring(0, charIndex + 1));
        charIndex++;
        setTimeout(typeNextChar, 45); // Slightly faster for intro
      } else {
        // Close intro after message is complete
        setTimeout(() => {
          setShowScenarioIntro(false);
        }, 3000); // Give time to read the complete message
      }
    };

    typeNextChar();
  };

  const startScenario2IntroTypewriter = () => {
    let charIndex = 0;

    // Play scenario 2 audio
    const playScenario2Audio = async () => {
      try {
        const audio = new Audio("/audio/Scenario-2.mp3");
        audio.volume = 0.7;
        await audio.play();
        console.log("Scenario 2 audio played successfully");
      } catch (error) {
        console.log("Scenario 2 audio play failed:", error);
      }
    };

    // Start audio playback
    playScenario2Audio();

    const typeNextChar = () => {
      if (charIndex < scenario2IntroMessage.length) {
        setScenario2IntroText(
          scenario2IntroMessage.substring(0, charIndex + 1)
        );
        charIndex++;
        setTimeout(typeNextChar, 45); // Same speed as first intro
      } else {
        // Close intro after message is complete
        setTimeout(() => {
          setShowScenario2Intro(false);
        }, 3000); // Give time to read the complete message
      }
    };

    typeNextChar();
  };

  const startScenario3IntroTypewriter = () => {
    const scenario3IntroMessage =
      "Ready for Round 3? Time to test your skills on a new platform!";
    let charIndex = 0;

    // Play scenario 3 audio
    const playScenario3Audio = async () => {
      try {
        const audio = new Audio("/audio/Scenario-3.mp3");
        audio.volume = 0.7;
        await audio.play();
        console.log("Scenario 3 audio played successfully");
      } catch (error) {
        console.log("Scenario 3 audio play failed:", error);
      }
    };

    // Start audio playback
    playScenario3Audio();

    const typeNextChar = () => {
      if (charIndex < scenario3IntroMessage.length) {
        setScenario3IntroText(
          scenario3IntroMessage.substring(0, charIndex + 1)
        );
        charIndex++;
        setTimeout(typeNextChar, 45);
      } else {
        setTimeout(() => {
          setShowScenario3Intro(false);
        }, 3000);
      }
    };

    typeNextChar();
  };

  const startScenario4IntroTypewriter = () => {
    const scenario4IntroMessage =
      "You're adapting well, Agent.\nBut every step forward means facing tougher deception.";
    let charIndex = 0;

    // Play scenario 4 audio
    const playScenario4Audio = async () => {
      try {
        const audio = new Audio("/audio/Scenario-4.mp3");
        audio.volume = 0.7;
        await audio.play();
        console.log("Scenario 4 audio played successfully");
      } catch (error) {
        console.log("Scenario 4 audio play failed:", error);
      }
    };

    // Start audio playback
    playScenario4Audio();

    const typeNextChar = () => {
      if (charIndex < scenario4IntroMessage.length) {
        setScenario4IntroText(
          scenario4IntroMessage.substring(0, charIndex + 1)
        );
        charIndex++;
        setTimeout(typeNextChar, 45);
      } else {
        setTimeout(() => {
          setShowScenario4Intro(false);
        }, 3000);
      }
    };

    typeNextChar();
  };

  const startScenario5IntroTypewriter = () => {
    const scenario5IntroMessage =
      "Well done, Agent.\nYour cyber-awareness has strengthened, but the real challenge begins now.";
    let charIndex = 0;

    // Play scenario 5 audio
    const playScenario5Audio = async () => {
      try {
        const audio = new Audio("/audio/Scenario-5.mp3");
        audio.volume = 0.7;
        await audio.play();
        console.log("Scenario 5 audio played successfully");
      } catch (error) {
        console.log("Scenario 5 audio play failed:", error);
      }
    };

    // Start audio playback
    playScenario5Audio();

    const typeNextChar = () => {
      if (charIndex < scenario5IntroMessage.length) {
        setScenario5IntroText(
          scenario5IntroMessage.substring(0, charIndex + 1)
        );
        charIndex++;
        setTimeout(typeNextChar, 45);
      } else {
        setTimeout(() => {
          setShowScenario5Intro(false);
        }, 3000);
      }
    };

    typeNextChar();
  };

  const startScenario6IntroTypewriter = () => {
    const scenario6IntroMessage =
      "Endgame Initiated.\nNo more training. No more hints. Let's see what you're really made of.";
    let charIndex = 0;

    // Play scenario 6 audio
    const playScenario6Audio = async () => {
      try {
        const audio = new Audio("/audio/Scenario-6.mp3");
        audio.volume = 0.7;
        await audio.play();
        console.log("Scenario 6 audio played successfully");
      } catch (error) {
        console.log("Scenario 6 audio play failed:", error);
      }
    };

    // Start audio playback
    playScenario6Audio();

    const typeNextChar = () => {
      if (charIndex < scenario6IntroMessage.length) {
        setScenario6IntroText(
          scenario6IntroMessage.substring(0, charIndex + 1)
        );
        charIndex++;
        setTimeout(typeNextChar, 45);
      } else {
        setTimeout(() => {
          setShowScenario6Intro(false);
        }, 3000);
      }
    };

    typeNextChar();
  };

  const startPopupSequence = () => {
    // Show all popups quickly in rapid succession
    fakePopups.forEach((_, index) => {
      setTimeout(() => {
        setCurrentPopupIndex(index);
      }, index * 50); // Very fast - 50ms between each popup
    });

    // End the popup sequence after all are shown
    setTimeout(() => {
      setShowGlitchPopups(false);
      setShowMascot(false);
    }, fakePopups.length * 50 + 800); // Wait for all popups + 800ms
  };

  // Audio prompt overlay
  if (showAudioPrompt) {
    return (
      <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50">
        <div className="bg-gradient-to-br from-slate-800 to-slate-900 p-6 rounded-xl shadow-2xl max-w-sm mx-4 text-center border border-slate-700">
          <div className="mb-4">
            <div className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full mb-3">
              <span className="text-lg">🔊</span>
            </div>
          </div>
          <h3 className="text-lg font-bold text-white mb-3">
            Audio Experience
          </h3>
          <p className="text-slate-300 text-sm mb-4">
            Click to interact with the website and enable audio.
          </p>
          <button
            onClick={handleEnableAudio}
            className="w-full px-4 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200 transform hover:scale-105 shadow-lg mb-2"
          >
            🎵 Enable Audio
          </button>
          <button
            onClick={() => {
              playClickSound();
              setShowAudioPrompt(false);
            }}
            className="w-full px-4 py-2 text-slate-400 hover:text-white transition-colors duration-200 text-sm"
          >
            Continue silently
          </button>
        </div>
      </div>
    );
  }

  // Mascot popup with typewriter effect
  if (showMascot) {
    return (
      <div className="fixed inset-0 bg-black flex items-center justify-center z-50 overflow-hidden">
        {/* Moving Grid Background */}
        <div className="absolute inset-0 opacity-30 sm:opacity-20">
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `
                linear-gradient(rgba(34, 197, 94, 0.45) 2px, transparent 2px),
                linear-gradient(90deg, rgba(34, 197, 94, 0.45) 2px, transparent 2px)
              `,
              backgroundSize: "30px 30px",
              transform: "translateX(0px) translateY(0px)",
              animation: "gridMove 15s linear infinite",
            }}
          />
        </div>

        {/* Floating Code Lines */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {/* Code snippets floating across screen */}
          <div className="absolute top-10 left-0 w-full">
            <div className="animate-pulse opacity-20">
              <pre
                className="text-green-400 font-mono text-xs leading-relaxed whitespace-nowrap"
                style={{ animation: "scrollLeft 25s linear infinite" }}
              >
                {`if (user.isVulnerable()) {
  exploit.execute();
  data.steal();
} else {
  security.bypass();
}`}
              </pre>
            </div>
          </div>

          <div className="absolute top-32 right-0 w-full">
            <div className="animate-pulse opacity-15 delay-1000">
              <pre
                className="text-cyan-400 font-mono text-xs leading-relaxed whitespace-nowrap text-right"
                style={{ animation: "scrollRight 30s linear infinite" }}
              >
                {`function hackMainframe() {
  const access = gainRootAccess();
  if (access.granted) {
    system.infiltrate();
    firewall.disable();
  }
}`}
              </pre>
            </div>
          </div>

          <div className="absolute bottom-40 right-0 w-full">
            <div className="animate-pulse opacity-10 delay-3000">
              <pre
                className="text-purple-400 font-mono text-xs leading-relaxed whitespace-nowrap text-right"
                style={{ animation: "scrollRight 20s linear infinite" }}
              >
                {`#!/bin/bash
nmap -sS -O target.com
hydra -l admin -P passwords.txt ssh://target
metasploit > use exploit/multi/handler`}
              </pre>
            </div>
          </div>

          <div className="absolute bottom-20 left-0 w-full">
            <div className="animate-pulse opacity-15 delay-4000">
              <pre
                className="text-yellow-400 font-mono text-xs leading-relaxed whitespace-nowrap"
                style={{ animation: "scrollLeft 28s linear infinite" }}
              >
                {`const decrypt = (data) => {
  return CryptoJS.AES.decrypt(data, secretKey).toString();
};
payload.inject(targetProcess);`}
              </pre>
            </div>
          </div>
        </div>

        {/* Terminal windows */}
        <div className="absolute top-20 left-20 w-64 h-32 bg-black/60 border border-green-500/20 rounded-lg overflow-hidden opacity-15">
          <div className="bg-green-500/10 px-3 py-1 text-green-400 font-mono text-xs">
            root@hackersim:~#
          </div>
          <div className="p-3 text-green-400 font-mono text-xs leading-relaxed">
            <div className="animate-pulse">
              $ sudo nmap -sV target.network
              <br />
              Starting Nmap scan...
              <br />
              Host is up (0.001s latency)
              <br />
              <span className="animate-ping">▋</span>
            </div>
          </div>
        </div>

        <div className="absolute bottom-20 right-20 w-60 h-28 bg-black/60 border border-cyan-500/20 rounded-lg overflow-hidden opacity-15">
          <div className="bg-cyan-500/10 px-3 py-1 text-cyan-400 font-mono text-xs">
            [BREACH DETECTED]
          </div>
          <div className="p-3 text-cyan-400 font-mono text-xs leading-relaxed">
            <div className="animate-pulse">
              System compromised...
              <br />
              Payload deployed: SUCCESS
              <br />
              <span className="animate-ping text-red-400">CRITICAL</span>
            </div>
          </div>
        </div>

        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-500/5 rounded-full blur-3xl animate-pulse" />
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl animate-pulse delay-1000" />
          <div className="absolute top-20 left-20 w-60 h-60 bg-green-500/3 rounded-full blur-2xl animate-pulse delay-2000" />

          {/* Floating particles */}
          <div className="absolute top-1/4 right-1/4 w-2 h-2 bg-green-400/20 rounded-full animate-bounce" />
          <div className="absolute bottom-1/3 left-1/3 w-1 h-1 bg-blue-400/25 rounded-full animate-pulse" />
          <div className="absolute top-1/2 left-1/4 w-3 h-3 bg-green-400/15 rounded-full animate-ping" />

          {/* Hacker symbols */}
          <div className="absolute top-40 right-1/3 text-green-400/8 text-4xl font-mono animate-pulse">
            {"</>"}
          </div>
          <div className="absolute bottom-60 left-1/4 text-cyan-400/8 text-3xl font-mono animate-pulse delay-1000">
            $
          </div>
          <div className="absolute top-1/3 left-1/6 text-red-400/8 text-3xl font-mono animate-pulse delay-2000">
            #
          </div>
        </div>

        {/* CSS Animation Styles */}
        <style
          dangerouslySetInnerHTML={{
            __html: `
            @keyframes gridMove {
              0% { transform: translate(0, 0); }
              100% { transform: translate(40px, 40px); }
            }
            @keyframes scrollLeft {
              0% { transform: translateX(100vw); }
              100% { transform: translateX(-100%); }
            }
            @keyframes scrollRight {
              0% { transform: translateX(-100vw); }
              100% { transform: translateX(100%); }
            }
            @keyframes popupHit {
              0% { 
                transform: translateY(-100vh) rotate(0deg) scale(0.8);
                opacity: 0;
              }
              70% { 
                transform: translateY(0) rotate(var(--rotation, 0deg)) scale(1.05);
                opacity: 1;
              }
              100% { 
                transform: translateY(0) rotate(var(--rotation, 0deg)) scale(1);
                opacity: 1;
              }
            }
            @keyframes glitchShake {
              0% { 
                transform: translateY(0) rotate(var(--rotation, 0deg)) scale(1);
              }
              10% { 
                transform: translateY(-2px) rotate(calc(var(--rotation, 0deg) + 1deg)) scale(1.01);
                filter: hue-rotate(90deg);
              }
              20% { 
                transform: translateY(1px) rotate(calc(var(--rotation, 0deg) - 1deg)) scale(0.99);
                filter: hue-rotate(0deg);
              }
              30% { 
                transform: translateY(-1px) rotate(calc(var(--rotation, 0deg) + 0.5deg)) scale(1.005);
                filter: hue-rotate(180deg);
              }
              40% { 
                transform: translateY(0px) rotate(var(--rotation, 0deg)) scale(1);
                filter: hue-rotate(270deg);
              }
              50% { 
                transform: translateY(1px) rotate(calc(var(--rotation, 0deg) - 0.5deg)) scale(0.995);
                filter: hue-rotate(0deg);
              }
              60% { 
                transform: translateY(-2px) rotate(calc(var(--rotation, 0deg) + 1.5deg)) scale(1.01);
                filter: hue-rotate(45deg);
              }
              70% { 
                transform: translateY(0px) rotate(var(--rotation, 0deg)) scale(1);
                filter: hue-rotate(0deg);
              }
              80% { 
                transform: translateY(1px) rotate(calc(var(--rotation, 0deg) - 1deg)) scale(0.99);
                filter: hue-rotate(135deg);
              }
              90% { 
                transform: translateY(-1px) rotate(calc(var(--rotation, 0deg) + 0.8deg)) scale(1.008);
                filter: hue-rotate(0deg);
              }
              100% { 
                transform: translateY(0) rotate(var(--rotation, 0deg)) scale(1);
                filter: hue-rotate(0deg);
              }
            }
          `,
          }}
        />

        <div className="relative z-10 max-w-4xl mx-4 text-center">
          {/* Large Mascot Image */}
          <div className="mb-6">
            <img
              src="/hacker.jpg"
              alt="Hacker Mascot"
              className="w-40 h-40 sm:w-56 sm:h-56 md:w-72 md:h-72 mx-auto shadow-2xl shadow-green-500/20"
            />
          </div>

          {/* Full-screen typewriter text */}
          <div className="bg-black/60 backdrop-blur-sm rounded-2xl p-4 sm:p-6 md:p-8 border border-green-500/10">
            <pre className="text-green-400 font-mono text-lg sm:text-lg md:text-xl lg:text-2xl leading-relaxed text-left whitespace-pre-wrap w-full">
              {currentText}
              <span className="animate-pulse">|</span>
            </pre>
          </div>
        </div>

        {/* Glitch Popup Overlay */}
        {showGlitchPopups && (
          <div className="fixed inset-0 z-50 pointer-events-none">
            {fakePopups.slice(0, currentPopupIndex + 1).map((popup, index) => {
              const positions = [
                { left: "0%", top: "0%" },
                { left: "70%", top: "2%" },
                { left: "15%", top: "12%" },
                { left: "82%", top: "18%" },
                { left: "3%", top: "28%" },
                { left: "45%", top: "35%" },
                { left: "75%", top: "42%" },
                { left: "20%", top: "55%" },
                { left: "0%", top: "68%" },
                { left: "55%", top: "75%" },
              ];

              const position = positions[index] || { left: "50%", top: "50%" };
              const rotation = Math.random() * 20 - 10; // More dramatic rotation
              const glitchDelay = Math.random() * 100; // Random glitch timing

              return (
                <div
                  key={index}
                  className="absolute"
                  style={
                    {
                      left: position.left,
                      top: position.top,
                      "--rotation": `${rotation}deg`,
                      animation: `popupHit 0.4s ease-out ${
                        index * 50
                      }ms forwards, glitchShake 0.1s infinite ${glitchDelay}ms`,
                    } as React.CSSProperties
                  }
                >
                  <div
                    className={`
                      w-96 border-2 border-gray-800 shadow-2xl
                      ${
                        popup.type === "error"
                          ? "bg-gray-200 border-red-600"
                          : ""
                      }
                      ${
                        popup.type === "warning"
                          ? "bg-gray-200 border-yellow-600"
                          : ""
                      }
                      ${
                        popup.type === "info"
                          ? "bg-gray-200 border-blue-600"
                          : ""
                      }
                      ${
                        popup.type === "success"
                          ? "bg-gray-200 border-green-600"
                          : ""
                      }
                    `}
                  >
                    {/* Classic Windows Title Bar */}
                    <div
                      className={`
                      flex items-center justify-between px-2 py-1 text-white text-sm font-bold
                      ${popup.type === "error" ? "bg-red-600" : ""}
                      ${popup.type === "warning" ? "bg-yellow-600" : ""}
                      ${popup.type === "info" ? "bg-blue-600" : ""}
                      ${popup.type === "success" ? "bg-green-600" : ""}
                    `}
                    >
                      <span>{popup.title}</span>
                      <div className="flex space-x-1">
                        <button className="w-5 h-4 bg-gray-300 border border-gray-600 text-black text-xs flex items-center justify-center hover:bg-gray-400">
                          _
                        </button>
                        <button className="w-5 h-4 bg-gray-300 border border-gray-600 text-black text-xs flex items-center justify-center hover:bg-gray-400">
                          ✕
                        </button>
                      </div>
                    </div>

                    {/* Dialog Content */}
                    <div className="p-4 bg-gray-200 text-black">
                      {/* Alert Icon and Message */}
                      <div className="flex items-start mb-4">
                        <div className="mr-4 text-3xl flex-shrink-0">
                          {popup.type === "error" && "❌"}
                          {popup.type === "warning" && "⚠️"}
                          {popup.type === "info" && "ℹ️"}
                          {popup.type === "success" && "✅"}
                        </div>
                        <div>
                          <p className="text-sm leading-relaxed font-sans">
                            {popup.message}
                          </p>
                        </div>
                      </div>

                      {/* Classic Windows Buttons */}
                      <div className="flex justify-end space-x-2">
                        <button className="px-4 py-1 bg-gray-300 border-2 border-gray-600 text-black text-sm font-bold hover:bg-gray-400 active:border-gray-400">
                          OK
                        </button>
                        <button className="px-4 py-1 bg-gray-300 border-2 border-gray-600 text-black text-sm font-bold hover:bg-gray-400 active:border-gray-400">
                          Cancel
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    );
  }

  // Scenario intro popup before first scenario
  if (showScenarioIntro) {
    return (
      <div className="fixed inset-0 bg-black flex items-center justify-center z-50 overflow-hidden">
        {/* Moving Grid Background */}
        <div className="absolute inset-0 opacity-30 sm:opacity-20">
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `
                linear-gradient(rgba(34, 197, 94, 0.45) 2px, transparent 2px),
                linear-gradient(90deg, rgba(34, 197, 94, 0.45) 2px, transparent 2px)
              `,
              backgroundSize: "30px 30px",
              transform: "translateX(0px) translateY(0px)",
              animation: "gridMove 15s linear infinite",
            }}
          />
        </div>

        {/* Floating Code Lines */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {/* Code snippets floating across screen */}
          <div className="absolute top-10 left-0 w-full">
            <div className="animate-pulse opacity-15">
              <pre
                className="text-green-400 font-mono text-xs leading-relaxed whitespace-nowrap"
                style={{ animation: "scrollLeft 25s linear infinite" }}
              >
                {`if (user.isVulnerable()) {
  exploit.execute();
  data.steal();
} else {
  security.bypass();
}`}
              </pre>
            </div>
          </div>

          <div className="absolute top-32 right-0 w-full">
            <div className="animate-pulse opacity-10 delay-1000">
              <pre
                className="text-cyan-400 font-mono text-xs leading-relaxed whitespace-nowrap text-right"
                style={{ animation: "scrollRight 30s linear infinite" }}
              >
                {`function hackMainframe() {
  const access = gainRootAccess();
  if (access.granted) {
    system.infiltrate();
    firewall.disable();
  }
}`}
              </pre>
            </div>
          </div>

          <div className="absolute bottom-40 right-0 w-full">
            <div className="animate-pulse opacity-8 delay-3000">
              <pre
                className="text-purple-400 font-mono text-xs leading-relaxed whitespace-nowrap text-right"
                style={{ animation: "scrollRight 20s linear infinite" }}
              >
                {`#!/bin/bash
nmap -sS -O target.com
hydra -l admin -P passwords.txt ssh://target
metasploit > use exploit/multi/handler`}
              </pre>
            </div>
          </div>

          <div className="absolute bottom-20 left-0 w-full">
            <div className="animate-pulse opacity-12 delay-4000">
              <pre
                className="text-yellow-400 font-mono text-xs leading-relaxed whitespace-nowrap"
                style={{ animation: "scrollLeft 28s linear infinite" }}
              >
                {`const decrypt = (data) => {
  return CryptoJS.AES.decrypt(data, secretKey).toString();
};
payload.inject(targetProcess);`}
              </pre>
            </div>
          </div>
        </div>

        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-500/5 rounded-full blur-3xl animate-pulse" />
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl animate-pulse delay-1000" />
          <div className="absolute top-20 left-20 w-60 h-60 bg-green-500/3 rounded-full blur-2xl animate-pulse delay-2000" />

          {/* Floating particles */}
          <div className="absolute top-1/4 right-1/4 w-2 h-2 bg-green-400/15 rounded-full animate-bounce" />
          <div className="absolute bottom-1/3 left-1/3 w-1 h-1 bg-blue-400/20 rounded-full animate-pulse" />
          <div className="absolute top-1/2 left-1/4 w-3 h-3 bg-green-400/10 rounded-full animate-ping" />

          {/* Hacker symbols */}
          <div className="absolute top-40 right-1/3 text-green-400/6 text-4xl font-mono animate-pulse">
            {"</>"}
          </div>
          <div className="absolute bottom-60 left-1/4 text-cyan-400/6 text-3xl font-mono animate-pulse delay-1000">
            $
          </div>
          <div className="absolute top-1/3 left-1/6 text-red-400/6 text-3xl font-mono animate-pulse delay-2000">
            #
          </div>
        </div>

        {/* CSS Animation Styles */}
        <style
          dangerouslySetInnerHTML={{
            __html: `
            @keyframes gridMove {
              0% { transform: translate(0, 0); }
              100% { transform: translate(40px, 40px); }
            }
            @keyframes scrollLeft {
              0% { transform: translateX(100vw); }
              100% { transform: translateX(-100%); }
            }
            @keyframes scrollRight {
              0% { transform: translateX(-100vw); }
              100% { transform: translateX(100%); }
            }
          `,
          }}
        />

        <div className="relative z-10 max-w-4xl mx-4 text-center">
          {/* Large Mascot Image */}
          <div className="mb-6">
            <img
              src="/hacker.jpg"
              alt="Hacker Mascot"
              className="w-40 h-40 sm:w-56 sm:h-56 md:w-72 md:h-72 mx-auto shadow-2xl shadow-green-500/20"
            />
          </div>

          {/* Full-screen typewriter text */}
          <div className="bg-black/60 backdrop-blur-sm rounded-2xl p-4 sm:p-6 md:p-8 border border-green-500/10">
            <pre className="text-green-400 font-mono text-lg sm:text-lg md:text-xl lg:text-2xl leading-relaxed text-left whitespace-pre-wrap w-full">
              {scenarioIntroText}
              <span className="animate-pulse">|</span>
            </pre>
          </div>
        </div>
      </div>
    );
  }

  // Scenario 2 intro popup after first scenario
  if (showScenario2Intro) {
    return (
      <div className="fixed inset-0 bg-black flex items-center justify-center z-50 overflow-hidden">
        {/* Moving Grid Background */}
        <div className="absolute inset-0 opacity-30 sm:opacity-20">
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `
                linear-gradient(rgba(34, 197, 94, 0.45) 2px, transparent 2px),
                linear-gradient(90deg, rgba(34, 197, 94, 0.45) 2px, transparent 2px)
              `,
              backgroundSize: "30px 30px",
              transform: "translateX(0px) translateY(0px)",
              animation: "gridMove 15s linear infinite",
            }}
          />
        </div>

        {/* Floating Code Lines */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {/* Code snippets floating across screen */}
          <div className="absolute top-10 left-0 w-full">
            <div className="animate-pulse opacity-15">
              <pre
                className="text-green-400 font-mono text-xs leading-relaxed whitespace-nowrap"
                style={{ animation: "scrollLeft 25s linear infinite" }}
              >
                {`if (user.isVulnerable()) {
  exploit.execute();
  data.steal();
} else {
  security.bypass();
}`}
              </pre>
            </div>
          </div>

          <div className="absolute top-32 right-0 w-full">
            <div className="animate-pulse opacity-10 delay-1000">
              <pre
                className="text-cyan-400 font-mono text-xs leading-relaxed whitespace-nowrap text-right"
                style={{ animation: "scrollRight 30s linear infinite" }}
              >
                {`function hackMainframe() {
  const access = gainRootAccess();
  if (access.granted) {
    system.infiltrate();
    firewall.disable();
  }
}`}
              </pre>
            </div>
          </div>

          <div className="absolute bottom-40 right-0 w-full">
            <div className="animate-pulse opacity-8 delay-3000">
              <pre
                className="text-purple-400 font-mono text-xs leading-relaxed whitespace-nowrap text-right"
                style={{ animation: "scrollRight 20s linear infinite" }}
              >
                {`#!/bin/bash
nmap -sS -O target.com
hydra -l admin -P passwords.txt ssh://target
metasploit > use exploit/multi/handler`}
              </pre>
            </div>
          </div>

          <div className="absolute bottom-20 left-0 w-full">
            <div className="animate-pulse opacity-12 delay-4000">
              <pre
                className="text-yellow-400 font-mono text-xs leading-relaxed whitespace-nowrap"
                style={{ animation: "scrollLeft 28s linear infinite" }}
              >
                {`const decrypt = (data) => {
  return CryptoJS.AES.decrypt(data, secretKey).toString();
};
payload.inject(targetProcess);`}
              </pre>
            </div>
          </div>
        </div>

        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-500/5 rounded-full blur-3xl animate-pulse" />
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl animate-pulse delay-1000" />
          <div className="absolute top-20 left-20 w-60 h-60 bg-green-500/3 rounded-full blur-2xl animate-pulse delay-2000" />

          {/* Floating particles */}
          <div className="absolute top-1/4 right-1/4 w-2 h-2 bg-green-400/15 rounded-full animate-bounce" />
          <div className="absolute bottom-1/3 left-1/3 w-1 h-1 bg-blue-400/20 rounded-full animate-pulse" />
          <div className="absolute top-1/2 left-1/4 w-3 h-3 bg-green-400/10 rounded-full animate-ping" />

          {/* Hacker symbols */}
          <div className="absolute top-40 right-1/3 text-green-400/6 text-4xl font-mono animate-pulse">
            {"</>"}
          </div>
          <div className="absolute bottom-60 left-1/4 text-cyan-400/6 text-3xl font-mono animate-pulse delay-1000">
            $
          </div>
          <div className="absolute top-1/3 left-1/6 text-red-400/6 text-3xl font-mono animate-pulse delay-2000">
            #
          </div>
        </div>

        {/* CSS Animation Styles */}
        <style
          dangerouslySetInnerHTML={{
            __html: `
            @keyframes gridMove {
              0% { transform: translate(0, 0); }
              100% { transform: translate(40px, 40px); }
            }
            @keyframes scrollLeft {
              0% { transform: translateX(100vw); }
              100% { transform: translateX(-100%); }
            }
            @keyframes scrollRight {
              0% { transform: translateX(-100vw); }
              100% { transform: translateX(100%); }
            }
          `,
          }}
        />

        <div className="relative z-10 max-w-4xl mx-4 text-center">
          {/* Large Mascot Image */}
          <div className="mb-6">
            <img
              src="/hacker.jpg"
              alt="Hacker Mascot"
              className="w-40 h-40 sm:w-56 sm:h-56 md:w-72 md:h-72 mx-auto shadow-2xl shadow-green-500/20"
            />
          </div>

          {/* Full-screen typewriter text */}
          <div className="bg-black/60 backdrop-blur-sm rounded-2xl p-4 sm:p-6 md:p-8 border border-green-500/10">
            <pre className="text-green-400 font-mono text-lg sm:text-lg md:text-xl lg:text-2xl leading-relaxed text-left whitespace-pre-wrap w-full">
              {scenario2IntroText}
              <span className="animate-pulse">|</span>
            </pre>
          </div>
        </div>
      </div>
    );
  }

  // Scenario 3 intro popup
  if (showScenario3Intro) {
    return (
      <div className="fixed inset-0 bg-black flex items-center justify-center z-50 overflow-hidden">
        {/* Moving Grid Background */}
        <div className="absolute inset-0 opacity-30 sm:opacity-20">
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `
                linear-gradient(rgba(34, 197, 94, 0.45) 2px, transparent 2px),
                linear-gradient(90deg, rgba(34, 197, 94, 0.45) 2px, transparent 2px)
              `,
              backgroundSize: "30px 30px",
              transform: "translateX(0px) translateY(0px)",
              animation: "gridMove 15s linear infinite",
            }}
          />
        </div>

        {/* Floating Code Lines */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-10 left-0 w-full">
            <div className="animate-pulse opacity-15">
              <pre
                className="text-green-400 font-mono text-xs leading-relaxed whitespace-nowrap"
                style={{ animation: "scrollLeft 25s linear infinite" }}
              >
                {`while (challenge.active) {
  skills.enhance();
  awareness.increase();
}`}
              </pre>
            </div>
          </div>

          <div className="absolute top-32 right-0 w-full">
            <div className="animate-pulse opacity-10 delay-1000">
              <pre
                className="text-cyan-400 font-mono text-xs leading-relaxed whitespace-nowrap text-right"
                style={{ animation: "scrollRight 30s linear infinite" }}
              >
                {`const levelUp = () => {
  player.experience += 100;
  player.rank = 'Advanced';
};`}
              </pre>
            </div>
          </div>

          <div className="absolute bottom-40 right-0 w-full">
            <div className="animate-pulse opacity-8 delay-3000">
              <pre
                className="text-purple-400 font-mono text-xs leading-relaxed whitespace-nowrap text-right"
                style={{ animation: "scrollRight 20s linear infinite" }}
              >
                {`// Round 3 initialized
success.probability = 0.85;
challenge.difficulty += 1;`}
              </pre>
            </div>
          </div>

          <div className="absolute bottom-20 left-0 w-full">
            <div className="animate-pulse opacity-12 delay-4000">
              <pre
                className="text-yellow-400 font-mono text-xs leading-relaxed whitespace-nowrap"
                style={{ animation: "scrollLeft 28s linear infinite" }}
              >
                {`player.ready = true;
nextLevel.load();
challenge.begin();`}
              </pre>
            </div>
          </div>
        </div>

        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-500/5 rounded-full blur-3xl animate-pulse" />
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl animate-pulse delay-1000" />
          <div className="absolute top-20 left-20 w-60 h-60 bg-green-500/3 rounded-full blur-2xl animate-pulse delay-2000" />

          <div className="absolute top-1/4 right-1/4 w-2 h-2 bg-green-400/15 rounded-full animate-bounce" />
          <div className="absolute bottom-1/3 left-1/3 w-1 h-1 bg-blue-400/20 rounded-full animate-pulse" />
          <div className="absolute top-1/2 left-1/4 w-3 h-3 bg-green-400/10 rounded-full animate-ping" />

          <div className="absolute top-40 right-1/3 text-green-400/6 text-4xl font-mono animate-pulse">
            {"🎯"}
          </div>
          <div className="absolute bottom-60 left-1/4 text-cyan-400/6 text-3xl font-mono animate-pulse delay-1000">
            $
          </div>
          <div className="absolute top-1/3 left-1/6 text-red-400/6 text-3xl font-mono animate-pulse delay-2000">
            #
          </div>
        </div>

        <style
          dangerouslySetInnerHTML={{
            __html: `
            @keyframes gridMove {
              0% { transform: translate(0, 0); }
              100% { transform: translate(40px, 40px); }
            }
            @keyframes scrollLeft {
              0% { transform: translateX(100vw); }
              100% { transform: translateX(-100%); }
            }
            @keyframes scrollRight {
              0% { transform: translateX(-100vw); }
              100% { transform: translateX(100%); }
            }
          `,
          }}
        />

        <div className="relative z-10 max-w-4xl mx-4 text-center">
          <div className="mb-6">
            <img
              src="/hacker.jpg"
              alt="Hacker Mascot"
              className="w-40 h-40 sm:w-56 sm:h-56 md:w-72 md:h-72 mx-auto shadow-2xl shadow-blue-500/20"
            />
          </div>

          <div className="bg-black/60 backdrop-blur-sm rounded-2xl p-4 sm:p-6 md:p-8 border border-blue-500/10">
            <pre className="text-blue-400 font-mono text-lg sm:text-lg md:text-xl lg:text-2xl leading-relaxed text-left whitespace-pre-wrap w-full">
              {scenario3IntroText}
              <span className="animate-pulse">|</span>
            </pre>
          </div>
        </div>
      </div>
    );
  }

  // Scenario 4 intro popup
  if (showScenario4Intro) {
    return (
      <div className="fixed inset-0 bg-black flex items-center justify-center z-50 overflow-hidden">
        {/* Moving Grid Background */}
        <div className="absolute inset-0 opacity-30 sm:opacity-20">
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `
                linear-gradient(rgba(34, 197, 94, 0.45) 2px, transparent 2px),
                linear-gradient(90deg, rgba(34, 197, 94, 0.45) 2px, transparent 2px)
              `,
              backgroundSize: "30px 30px",
              transform: "translateX(0px) translateY(0px)",
              animation: "gridMove 15s linear infinite",
            }}
          />
        </div>

        {/* Floating Code Lines */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-10 left-0 w-full">
            <div className="animate-pulse opacity-15">
              <pre
                className="text-green-400 font-mono text-xs leading-relaxed whitespace-nowrap"
                style={{ animation: "scrollLeft 25s linear infinite" }}
              >
                {`skillLevel.upgrade();
detection.rate += 25;
confidence.boost();`}
              </pre>
            </div>
          </div>

          <div className="absolute top-32 right-0 w-full">
            <div className="animate-pulse opacity-10 delay-1000">
              <pre
                className="text-cyan-400 font-mono text-xs leading-relaxed whitespace-nowrap text-right"
                style={{ animation: "scrollRight 30s linear infinite" }}
              >
                {`function levelUp() {
  player.skills.increment();
  achievements.unlock('Advanced');
}`}
              </pre>
            </div>
          </div>

          <div className="absolute bottom-40 right-0 w-full">
            <div className="animate-pulse opacity-8 delay-3000">
              <pre
                className="text-purple-400 font-mono text-xs leading-relaxed whitespace-nowrap text-right"
                style={{ animation: "scrollRight 20s linear infinite" }}
              >
                {`// Progress tracking
completedLevels = 3;
nextChallenge.prepare();`}
              </pre>
            </div>
          </div>

          <div className="absolute bottom-20 left-0 w-full">
            <div className="animate-pulse opacity-12 delay-4000">
              <pre
                className="text-yellow-400 font-mono text-xs leading-relaxed whitespace-nowrap"
                style={{ animation: "scrollLeft 28s linear infinite" }}
              >
                {`cybersecurity.awareness = 'HIGH';
player.ready = true;`}
              </pre>
            </div>
          </div>
        </div>

        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-orange-500/5 rounded-full blur-3xl animate-pulse" />
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl animate-pulse delay-1000" />
          <div className="absolute top-20 left-20 w-60 h-60 bg-green-500/3 rounded-full blur-2xl animate-pulse delay-2000" />

          <div className="absolute top-1/4 right-1/4 w-2 h-2 bg-orange-400/15 rounded-full animate-bounce" />
          <div className="absolute bottom-1/3 left-1/3 w-1 h-1 bg-blue-400/20 rounded-full animate-pulse" />
          <div className="absolute top-1/2 left-1/4 w-3 h-3 bg-orange-400/10 rounded-full animate-ping" />

          <div className="absolute top-40 right-1/3 text-orange-400/6 text-4xl font-mono animate-pulse">
            {"🚀"}
          </div>
          <div className="absolute bottom-60 left-1/4 text-cyan-400/6 text-3xl font-mono animate-pulse delay-1000">
            $
          </div>
          <div className="absolute top-1/3 left-1/6 text-red-400/6 text-3xl font-mono animate-pulse delay-2000">
            #
          </div>
        </div>

        <style
          dangerouslySetInnerHTML={{
            __html: `
            @keyframes gridMove {
              0% { transform: translate(0, 0); }
              100% { transform: translate(40px, 40px); }
            }
            @keyframes scrollLeft {
              0% { transform: translateX(100vw); }
              100% { transform: translateX(-100%); }
            }
            @keyframes scrollRight {
              0% { transform: translateX(-100vw); }
              100% { transform: translateX(100%); }
            }
          `,
          }}
        />

        <div className="relative z-10 max-w-4xl mx-4 text-center">
          <div className="mb-6">
            <img
              src="/hacker.jpg"
              alt="Hacker Mascot"
              className="w-40 h-40 sm:w-56 sm:h-56 md:w-72 md:h-72 mx-auto shadow-2xl shadow-orange-500/20"
            />
          </div>

          <div className="bg-black/60 backdrop-blur-sm rounded-2xl p-4 sm:p-6 md:p-8 border border-orange-500/10">
            <pre className="text-orange-400 font-mono text-lg sm:text-lg md:text-xl lg:text-2xl leading-relaxed text-left whitespace-pre-wrap w-full">
              {scenario4IntroText}
              <span className="animate-pulse">|</span>
            </pre>
          </div>
        </div>
      </div>
    );
  }

  // Scenario 5 intro popup
  if (showScenario5Intro) {
    return (
      <div className="fixed inset-0 bg-black flex items-center justify-center z-50 overflow-hidden">
        {/* Moving Grid Background */}
        <div className="absolute inset-0 opacity-30 sm:opacity-20">
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `
                linear-gradient(rgba(34, 197, 94, 0.45) 2px, transparent 2px),
                linear-gradient(90deg, rgba(34, 197, 94, 0.45) 2px, transparent 2px)
              `,
              backgroundSize: "30px 30px",
              transform: "translateX(0px) translateY(0px)",
              animation: "gridMove 15s linear infinite",
            }}
          />
        </div>

        {/* Floating Code Lines */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-10 left-0 w-full">
            <div className="animate-pulse opacity-15">
              <pre
                className="text-green-400 font-mono text-xs leading-relaxed whitespace-nowrap"
                style={{ animation: "scrollLeft 25s linear infinite" }}
              >
                {`expertise.level = 'EXPERT';
threats.detected += 50;
security.awareness = 'MAXIMUM';`}
              </pre>
            </div>
          </div>

          <div className="absolute top-32 right-0 w-full">
            <div className="animate-pulse opacity-10 delay-1000">
              <pre
                className="text-cyan-400 font-mono text-xs leading-relaxed whitespace-nowrap text-right"
                style={{ animation: "scrollRight 30s linear infinite" }}
              >
                {`function nearlyComplete() {
  progress.percentage = 85;
  finalChallenge.prepare();
}`}
              </pre>
            </div>
          </div>

          <div className="absolute bottom-40 right-0 w-full">
            <div className="animate-pulse opacity-8 delay-3000">
              <pre
                className="text-purple-400 font-mono text-xs leading-relaxed whitespace-nowrap text-right"
                style={{ animation: "scrollRight 20s linear infinite" }}
              >
                {`// Almost at the finish line
cyberWarrior.strength += 100;
finalLevel.unlock();`}
              </pre>
            </div>
          </div>

          <div className="absolute bottom-20 left-0 w-full">
            <div className="animate-pulse opacity-12 delay-4000">
              <pre
                className="text-yellow-400 font-mono text-xs leading-relaxed whitespace-nowrap"
                style={{ animation: "scrollLeft 28s linear infinite" }}
              >
                {`mastery.approaching = true;
oneMoreChallenge.remaining();`}
              </pre>
            </div>
          </div>
        </div>

        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl animate-pulse" />
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-pink-500/5 rounded-full blur-3xl animate-pulse delay-1000" />
          <div className="absolute top-20 left-20 w-60 h-60 bg-green-500/3 rounded-full blur-2xl animate-pulse delay-2000" />

          <div className="absolute top-1/4 right-1/4 w-2 h-2 bg-purple-400/15 rounded-full animate-bounce" />
          <div className="absolute bottom-1/3 left-1/3 w-1 h-1 bg-pink-400/20 rounded-full animate-pulse" />
          <div className="absolute top-1/2 left-1/4 w-3 h-3 bg-purple-400/10 rounded-full animate-ping" />

          <div className="absolute top-40 right-1/3 text-purple-400/6 text-4xl font-mono animate-pulse">
            {"⚡"}
          </div>
          <div className="absolute bottom-60 left-1/4 text-cyan-400/6 text-3xl font-mono animate-pulse delay-1000">
            $
          </div>
          <div className="absolute top-1/3 left-1/6 text-red-400/6 text-3xl font-mono animate-pulse delay-2000">
            #
          </div>
        </div>

        <style
          dangerouslySetInnerHTML={{
            __html: `
            @keyframes gridMove {
              0% { transform: translate(0, 0); }
              100% { transform: translate(40px, 40px); }
            }
            @keyframes scrollLeft {
              0% { transform: translateX(100vw); }
              100% { transform: translateX(-100%); }
            }
            @keyframes scrollRight {
              0% { transform: translateX(-100vw); }
              100% { transform: translateX(100%); }
            }
          `,
          }}
        />

        <div className="relative z-10 max-w-4xl mx-4 text-center">
          <div className="mb-6">
            <img
              src="/hacker.jpg"
              alt="Hacker Mascot"
              className="w-40 h-40 sm:w-56 sm:h-56 md:w-72 md:h-72 mx-auto shadow-2xl shadow-purple-500/20"
            />
          </div>

          <div className="bg-black/60 backdrop-blur-sm rounded-2xl p-4 sm:p-6 md:p-8 border border-purple-500/10">
            <pre className="text-purple-400 font-mono text-lg sm:text-lg md:text-xl lg:text-2xl leading-relaxed text-left whitespace-pre-wrap w-full">
              {scenario5IntroText}
              <span className="animate-pulse">|</span>
            </pre>
          </div>
        </div>
      </div>
    );
  }

  // Scenario 6 intro popup
  if (showScenario6Intro) {
    return (
      <div className="fixed inset-0 bg-black flex items-center justify-center z-50 overflow-hidden">
        {/* Moving Grid Background */}
        <div className="absolute inset-0 opacity-30 sm:opacity-20">
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `
                linear-gradient(rgba(34, 197, 94, 0.45) 2px, transparent 2px),
                linear-gradient(90deg, rgba(34, 197, 94, 0.45) 2px, transparent 2px)
              `,
              backgroundSize: "30px 30px",
              transform: "translateX(0px) translateY(0px)",
              animation: "gridMove 15s linear infinite",
            }}
          />
        </div>

        {/* Floating Code Lines */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-10 left-0 w-full">
            <div className="animate-pulse opacity-15">
              <pre
                className="text-green-400 font-mono text-xs leading-relaxed whitespace-nowrap"
                style={{ animation: "scrollLeft 25s linear infinite" }}
              >
                {`finalChallenge.activate();
cyberWarrior.ready = true;
victory.awaits();`}
              </pre>
            </div>
          </div>

          <div className="absolute top-32 right-0 w-full">
            <div className="animate-pulse opacity-10 delay-1000">
              <pre
                className="text-cyan-400 font-mono text-xs leading-relaxed whitespace-nowrap text-right"
                style={{ animation: "scrollRight 30s linear infinite" }}
              >
                {`function finalBoss() {
  skills.test(ULTIMATE_LEVEL);
  return champion.emerge();
}`}
              </pre>
            </div>
          </div>

          <div className="absolute bottom-40 right-0 w-full">
            <div className="animate-pulse opacity-8 delay-3000">
              <pre
                className="text-purple-400 font-mono text-xs leading-relaxed whitespace-nowrap text-right"
                style={{ animation: "scrollRight 20s linear infinite" }}
              >
                {`// The ultimate test
mastery.level = 'LEGENDARY';
champion.prepare();`}
              </pre>
            </div>
          </div>

          <div className="absolute bottom-20 left-0 w-full">
            <div className="animate-pulse opacity-12 delay-4000">
              <pre
                className="text-yellow-400 font-mono text-xs leading-relaxed whitespace-nowrap"
                style={{ animation: "scrollLeft 28s linear infinite" }}
              >
                {`legend.begin();
victory.achieve();
warrior.crowned();`}
              </pre>
            </div>
          </div>
        </div>

        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-yellow-500/5 rounded-full blur-3xl animate-pulse" />
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-red-500/5 rounded-full blur-3xl animate-pulse delay-1000" />
          <div className="absolute top-20 left-20 w-60 h-60 bg-green-500/3 rounded-full blur-2xl animate-pulse delay-2000" />

          <div className="absolute top-1/4 right-1/4 w-2 h-2 bg-yellow-400/15 rounded-full animate-bounce" />
          <div className="absolute bottom-1/3 left-1/3 w-1 h-1 bg-red-400/20 rounded-full animate-pulse" />
          <div className="absolute top-1/2 left-1/4 w-3 h-3 bg-yellow-400/10 rounded-full animate-ping" />

          <div className="absolute top-40 right-1/3 text-yellow-400/6 text-4xl font-mono animate-pulse">
            {"🏆"}
          </div>
          <div className="absolute bottom-60 left-1/4 text-cyan-400/6 text-3xl font-mono animate-pulse delay-1000">
            $
          </div>
          <div className="absolute top-1/3 left-1/6 text-red-400/6 text-3xl font-mono animate-pulse delay-2000">
            #
          </div>
        </div>

        <style
          dangerouslySetInnerHTML={{
            __html: `
            @keyframes gridMove {
              0% { transform: translate(0, 0); }
              100% { transform: translate(40px, 40px); }
            }
            @keyframes scrollLeft {
              0% { transform: translateX(100vw); }
              100% { transform: translateX(-100%); }
            }
            @keyframes scrollRight {
              0% { transform: translateX(-100vw); }
              100% { transform: translateX(100%); }
            }
          `,
          }}
        />

        <div className="relative z-10 max-w-4xl mx-4 text-center">
          <div className="mb-6">
            <img
              src="/hacker.jpg"
              alt="Hacker Mascot"
              className="w-40 h-40 sm:w-56 sm:h-56 md:w-72 md:h-72 mx-auto shadow-2xl shadow-yellow-500/20"
            />
          </div>

          <div className="bg-black/60 backdrop-blur-sm rounded-2xl p-4 sm:p-6 md:p-8 border border-yellow-500/10">
            <pre className="text-yellow-400 font-mono text-lg sm:text-lg md:text-xl lg:text-2xl leading-relaxed text-left whitespace-pre-wrap w-full">
              {scenario6IntroText}
              <span className="animate-pulse">|</span>
            </pre>
          </div>
        </div>
      </div>
    );
  }

  const handleUserSubmit = (user: User) => {
    playClickSound();
    setSurveyState((prev) => ({
      ...prev,
      user,
    }));

    // Show scenario intro popup for users aged 10-17
    if (user.age >= 10 && user.age <= 17) {
      setShowScenarioIntro(true);
      startScenarioIntroTypewriter();
    }
  };

  const handleResponse = (optionIndex: number) => {
    playClickSound();
    if (!surveyState.user) return;

    const scenarios = getScenarios(surveyState.user.language);
    // Since we only support 10-17, get the teen scenarios directly
    const teenScenarios = scenarios.find(
      (group) => group.min === 10 && group.max === 17
    );

    if (!teenScenarios) return;

    const currentScenario =
      teenScenarios.scenarios[surveyState.currentScenario];

    // Determine risk level based on the selected option
    let riskLevel: "high_risk" | "medium_risk" | "low_risk" = "low_risk";

    if (currentScenario.riskLevels.high_risk.includes(optionIndex)) {
      riskLevel = "high_risk";
    } else if (currentScenario.riskLevels.medium_risk.includes(optionIndex)) {
      riskLevel = "medium_risk";
    } else {
      riskLevel = "low_risk";
    }

    const response: Response = {
      scenarioId: currentScenario.id,
      selectedOption: optionIndex,
      riskLevel,
    };

    const newResponses = [...surveyState.responses, response];
    const nextScenario = surveyState.currentScenario + 1;
    const isComplete = nextScenario >= teenScenarios.scenarios.length;

    setSurveyState((prev) => ({
      ...prev,
      responses: newResponses,
      currentScenario: nextScenario,
      isComplete,
    }));

    // Show scenario 2 intro popup after completing scenario 1
    if (
      surveyState.currentScenario === 0 && // Just finished scenario 1 (0-indexed)
      !isComplete
    ) {
      setShowScenario2Intro(true);
      startScenario2IntroTypewriter();
    }

    // Show scenario 3 intro popup after completing scenario 2
    if (
      surveyState.currentScenario === 1 && // Just finished scenario 2 (0-indexed)
      !isComplete
    ) {
      setShowScenario3Intro(true);
      startScenario3IntroTypewriter();
    }

    // Show scenario 4 intro popup after completing scenario 3
    if (
      surveyState.currentScenario === 2 && // Just finished scenario 3 (0-indexed)
      !isComplete
    ) {
      setShowScenario4Intro(true);
      startScenario4IntroTypewriter();
    }

    // Show scenario 5 intro popup after completing scenario 4
    if (
      surveyState.currentScenario === 3 && // Just finished scenario 4 (0-indexed)
      !isComplete
    ) {
      setShowScenario5Intro(true);
      startScenario5IntroTypewriter();
    }

    // Show scenario 6 intro popup after completing scenario 5
    if (
      surveyState.currentScenario === 4 && // Just finished scenario 5 (0-indexed)
      !isComplete
    ) {
      setShowScenario6Intro(true);
      startScenario6IntroTypewriter();
    }
  };

  const handleRestart = () => {
    playClickSound();
    setSurveyState({
      user: null,
      currentScenario: 0,
      responses: [],
      isComplete: false,
    });
  };

  // Show initial form if no user
  if (!surveyState.user) {
    return (
      <HackerBackground>
        <InitialForm
          onSubmit={handleUserSubmit}
          playClickSound={playClickSound}
        />
      </HackerBackground>
    );
  }

  // Show scenario intro popup if it's active
  if (showScenarioIntro) {
    return (
      <HackerBackground>
        <div className="min-h-screen flex items-center justify-center">
          {/* This will be handled by the scenario intro popup above */}
        </div>
      </HackerBackground>
    );
  }

  // Show scenario 2 intro popup if it's active
  if (showScenario2Intro) {
    return (
      <HackerBackground>
        <div className="min-h-screen flex items-center justify-center">
          {/* This will be handled by the scenario 2 intro popup above */}
        </div>
      </HackerBackground>
    );
  }

  // Show scenario 3 intro popup if it's active
  if (showScenario3Intro) {
    return (
      <HackerBackground>
        <div className="min-h-screen flex items-center justify-center">
          {/* This will be handled by the scenario 3 intro popup above */}
        </div>
      </HackerBackground>
    );
  }

  // Show scenario 4 intro popup if it's active
  if (showScenario4Intro) {
    return (
      <HackerBackground>
        <div className="min-h-screen flex items-center justify-center">
          {/* This will be handled by the scenario 4 intro popup above */}
        </div>
      </HackerBackground>
    );
  }

  // Show scenario 5 intro popup if it's active
  if (showScenario5Intro) {
    return (
      <HackerBackground>
        <div className="min-h-screen flex items-center justify-center">
          {/* This will be handled by the scenario 5 intro popup above */}
        </div>
      </HackerBackground>
    );
  }

  // Show scenario 6 intro popup if it's active
  if (showScenario6Intro) {
    return (
      <HackerBackground>
        <div className="min-h-screen flex items-center justify-center">
          {/* This will be handled by the scenario 6 intro popup above */}
        </div>
      </HackerBackground>
    );
  }

  // Show results if complete
  if (surveyState.isComplete) {
    return (
      <HackerBackground>
        <Results
          user={surveyState.user}
          responses={surveyState.responses}
          onRestart={handleRestart}
          playClickSound={playClickSound}
        />
      </HackerBackground>
    );
  }

  // Show survey
  const scenarios = getScenarios(surveyState.user.language);
  // Since we only support 13-17, get the teen scenarios directly
  const teenScenarios = scenarios.find(
    (group) => group.min === 13 && group.max === 17
  );

  if (!teenScenarios) {
    return (
      <HackerBackground>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center relative z-10">
            <div className="mb-8">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-red-500 to-orange-600 rounded-full mb-6 shadow-2xl shadow-red-500/25">
                <span className="text-2xl">⚠️</span>
              </div>
            </div>
            <h2 className="text-3xl font-bold bg-gradient-to-r from-green-400 via-green-300 to-green-200 bg-clip-text text-transparent mb-4">
              Scenarios Not Available
            </h2>
            <p className="text-green-300 mb-8 text-lg max-w-md">
              Teen scenarios are not available for your language.
            </p>
            <button
              onClick={handleRestart}
              className="px-8 py-4 bg-gradient-to-r from-green-600 to-green-700 text-black font-semibold rounded-xl hover:from-green-500 hover:to-green-600 transition-all duration-200 transform hover:scale-105 shadow-xl shadow-green-500/25"
            >
              Start Over
            </button>
          </div>
        </div>
      </HackerBackground>
    );
  }

  const currentScenario = teenScenarios.scenarios[surveyState.currentScenario];

  return (
    <HackerBackground>
      <Survey
        scenario={currentScenario}
        currentQuestion={surveyState.currentScenario + 1}
        totalQuestions={teenScenarios.scenarios.length}
        onResponse={handleResponse}
        userLanguage={surveyState.user.language}
        playClickSound={playClickSound}
      />
    </HackerBackground>
  );
}

export default App;
