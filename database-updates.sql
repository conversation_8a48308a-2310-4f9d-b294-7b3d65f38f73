-- Database updates to change teen_responses age range from 13-17 to 10-17
-- Run these queries in your Supabase SQL Editor

-- 1. Check current age distribution in teen_responses table
SELECT age, COUNT(*) as count
FROM teen_responses
GROUP BY age
ORDER BY age;

-- 2. Check table structure to ensure all columns exist
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_name = 'teen_responses'
ORDER BY ordinal_position;

-- 3. CRITICAL: Check all existing constraints on teen_responses table
SELECT conname, pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint
WHERE conrelid = 'teen_responses'::regclass
AND contype = 'c';

-- 4. Drop ALL existing age-related constraints (this is the key fix)
-- Replace 'constraint_name_here' with the actual constraint name from step 3
-- You may need to run this multiple times for different constraint names
DO $$
DECLARE
    constraint_record RECORD;
BEGIN
    FOR constraint_record IN
        SELECT conname
        FROM pg_constraint
        WHERE conrelid = 'teen_responses'::regclass
        AND contype = 'c'
        AND pg_get_constraintdef(oid) LIKE '%age%'
    LOOP
        EXECUTE 'ALTER TABLE teen_responses DROP CONSTRAINT IF EXISTS ' || constraint_record.conname;
    END LOOP;
END $$;

-- 5. Add the new correct constraint
ALTER TABLE teen_responses
ADD CONSTRAINT teen_responses_age_check_10_17
CHECK (age >= 10 AND age <= 17);

-- 4. Update table comment to reflect new age range
COMMENT ON TABLE teen_responses IS 'Detailed responses for users aged 10-17';

-- 6. Verify the constraint was added successfully
SELECT conname, pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint
WHERE conrelid = 'teen_responses'::regclass
AND contype = 'c';

-- 6. Check for any existing data that might violate the new constraint
SELECT COUNT(*) as invalid_ages_count
FROM teen_responses
WHERE age < 10 OR age > 17;

-- 7. If there are invalid ages, you can either:
-- Option A: Delete them (uncomment if needed)
-- DELETE FROM teen_responses WHERE age < 10 OR age > 17;

-- Option B: Update them to valid range (uncomment if needed)
-- UPDATE teen_responses SET age = 10 WHERE age < 10;
-- UPDATE teen_responses SET age = 17 WHERE age > 17;

-- 8. Test the constraint by trying to insert invalid ages (should fail)
-- Uncomment these lines to test (they should fail):
-- INSERT INTO teen_responses (response_id, name, age, preferred_language, high_risk, medium_risk, low_risk, overall_risk)
-- VALUES (999, 'Test User', 9, 'english', 0, 0, 1, 'Low');  -- Should fail (age 9)

-- INSERT INTO teen_responses (response_id, name, age, preferred_language, high_risk, medium_risk, low_risk, overall_risk)
-- VALUES (999, 'Test User', 18, 'english', 0, 0, 1, 'Low'); -- Should fail (age 18)

-- 9. Test valid age insertion (should succeed)
-- Uncomment this line to test (should succeed):
-- INSERT INTO teen_responses (response_id, name, age, preferred_language, high_risk, medium_risk, low_risk, overall_risk)
-- VALUES (999, 'Test User', 10, 'english', 0, 0, 1, 'Low'); -- Should succeed (age 10)

-- Clean up test data (run after testing)
-- DELETE FROM teen_responses WHERE response_id = 999;
