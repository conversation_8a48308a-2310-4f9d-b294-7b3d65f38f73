-- Script to identify duplicate scam_risk_results tables
-- Run these queries in your Supabase SQL Editor

-- 1. Check all tables with similar names in all schemas
SELECT schemaname, tablename, tableowner
FROM pg_tables 
WHERE tablename LIKE '%scam_risk_results%'
ORDER BY schemaname, tablename;

-- 2. Check the structure of tables in different schemas
-- Public schema (default)
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'scam_risk_results' 
ORDER BY ordinal_position;

-- 3. Check if there are tables in other schemas
SELECT column_name, data_type, is_nullable, table_schema
FROM information_schema.columns 
WHERE table_name = 'scam_risk_results' 
ORDER BY table_schema, ordinal_position;

-- 4. Count records in public.scam_risk_results
SELECT COUNT(*) as record_count, 'public.scam_risk_results' as table_name
FROM public.scam_risk_results;

-- 5. Check the most recent records in public schema
SELECT id, name, age, preferred_language, overall_risk, created_at
FROM public.scam_risk_results 
ORDER BY created_at DESC 
LIMIT 10;

-- 6. Check if your app is accidentally using a different schema
-- Look for any other scam_risk_results tables
SELECT 
    n.nspname as schema_name,
    c.relname as table_name,
    pg_size_pretty(pg_total_relation_size(c.oid)) as size
FROM pg_class c
LEFT JOIN pg_namespace n ON n.oid = c.relnamespace
WHERE c.relkind = 'r'
AND c.relname = 'scam_risk_results'
ORDER BY schema_name;

-- 7. If you find multiple tables, check which one has your 100+ records
-- Uncomment and modify schema names as needed:
-- SELECT COUNT(*) as record_count, 'schema1.scam_risk_results' as table_name
-- FROM schema1.scam_risk_results;

-- SELECT COUNT(*) as record_count, 'schema2.scam_risk_results' as table_name  
-- FROM schema2.scam_risk_results;
