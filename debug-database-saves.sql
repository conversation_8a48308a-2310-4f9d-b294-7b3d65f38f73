-- Debug script to check what's being saved to both tables
-- Run these queries in your Supabase SQL Editor

-- 1. Check recent entries in scam_risk_results (main table)
SELECT id, name, age, preferred_language, overall_risk, created_at
FROM scam_risk_results 
ORDER BY created_at DESC 
LIMIT 10;

-- 2. Check recent entries in teen_responses table
SELECT id, response_id, name, age, preferred_language, overall_risk, created_at
FROM teen_responses 
ORDER BY created_at DESC 
LIMIT 10;

-- 3. Check if there are any entries in scam_risk_results that don't have corresponding teen_responses
SELECT sr.id, sr.name, sr.age, sr.created_at,
       CASE WHEN tr.response_id IS NULL THEN 'MISSING' ELSE 'EXISTS' END as teen_response_status
FROM scam_risk_results sr
LEFT JOIN teen_responses tr ON sr.id = tr.response_id
WHERE sr.age >= 10 AND sr.age <= 17
ORDER BY sr.created_at DESC
LIMIT 10;

-- 4. Check table structures to ensure they match expectations
-- scam_risk_results structure
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'scam_risk_results' 
ORDER BY ordinal_position;

-- teen_responses structure  
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'teen_responses' 
ORDER BY ordinal_position;

-- 5. Check for any RLS (Row Level Security) policies that might be blocking inserts
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
FROM pg_policies 
WHERE tablename IN ('scam_risk_results', 'teen_responses');

-- 6. Check if there are any triggers on the tables
SELECT trigger_name, event_manipulation, event_object_table, action_statement
FROM information_schema.triggers
WHERE event_object_table IN ('scam_risk_results', 'teen_responses');

-- 7. Test insert into scam_risk_results (should work)
-- Uncomment to test:
-- INSERT INTO scam_risk_results (name, age, preferred_language, high_risk, medium_risk, low_risk, overall_risk)
-- VALUES ('Debug Test', 15, 'english', 1, 2, 3, 'Medium');

-- 8. Get the ID of the test insert and try teen_responses
-- Replace 'LAST_INSERT_ID' with the actual ID from step 7
-- INSERT INTO teen_responses (response_id, name, age, preferred_language, high_risk, medium_risk, low_risk, overall_risk)
-- VALUES (LAST_INSERT_ID, 'Debug Test', 15, 'english', 1, 2, 3, 'Medium');

-- 9. Clean up test data
-- DELETE FROM teen_responses WHERE name = 'Debug Test';
-- DELETE FROM scam_risk_results WHERE name = 'Debug Test';
